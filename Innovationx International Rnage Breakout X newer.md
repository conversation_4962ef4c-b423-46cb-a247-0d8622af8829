<!--
Explanation of content:

1. Pre-Testing Setup:
   - Compilation Test: Instructions to compile the EA using MetaEditor.
   - Basic Configuration: Lists recommended test settings for parameters like LotSize, RiskAmount, and trading hours.

2. Testing Phases:
   - Phase 1 covers fundamental tests (range identification, order placement, risk management).
   - Phase 2 introduces more advanced scenarios (enhanced fakeout logic, multi-trade configurations, and dynamic stop adjustments).
   - Phase 3 focuses on stress testing under conditions such as high volatility and session transitions.

3. Monitoring Checklist:
   - Ensures that both the info panel and MT4 logs reflect correct behavior (e.g., proper risk calculations, order placements, and system resets).

4. Common Issues and Solutions:
   - Provides troubleshooting tips (e.g., issues with range detection or pending orders) to help diagnose and resolve potential problems.

5. Production Deployment & Support:
   - Outlines requirements for moving to a live environment, including VPS setup, initial live trade monitoring, and scheduled performance reviews.
-->

# Innovationx Range Breakout EA - Testing Guide

## Pre-Testing Setup

### 1. Compilation Test
1. Open MetaEditor (F4 in MT4)
2. Open the file: `Innovationx International Rnage Breakout X.mq4`
3. Click "Compile" (F7)
4. Verify no compilation errors
5. Check that `.ex4` file is generated

### 2. Basic Configuration Test
```
// Recommended test settings:
LotSize = 0.01
InitialTradeRiskPercent = 1.0
UseFixedRisk = false
RiskAmount = 10.0
TradeTargetR = 1.5
StopLossMethod = SL_RANGE
UseBreakEven = true
BreakEvenPoints = 300 (for Forex) / 3000 (for Indices)
UseTrailingStop = true
TrailingStopPoints = 500 (for Forex) / 5000 (for Indices)
RangeStartHour = 0
RangeStartMinute = 0
RangeEndHour = 6
RangeEndMinute = 0
MaxDailyLoss = 2.0
MaxWeeklyLoss = 5.0
EnableStatsPanel = true
EnableAlerts = true
```

## Testing Phases

### Phase 1: Basic Functionality (Demo Account)
1. **Range Identification Test**
   - Attach EA to EURUSD M15 chart
   - Set range times: 00:00 to 06:00
   - Wait for range end time
   - Verify range box appears on chart
   - Check info panel shows range high/low values

2. **Pending Order Placement Test**
   - After range identification
   - Verify buy stop above range high
   - Verify sell stop below range low
   - Check order comments and magic numbers
   - Verify stop loss and take profit levels

3. **Risk Management Test**
   - Test with different lot sizes
   - Verify risk calculations in info panel
   - Test daily loss limit (set low value like 0.5%)
   - Verify trading stops when limit hit

### Phase 2: Advanced Features (Demo Account)
1. **Enhanced Fakeout Test**
   ```
   EnableCustomTradingHours = true
   CustomTradingStartHour = 8
   CustomTradingEndHour = 17
   EnableEnhancedFakeout = true
   ```
   - Test during trading hours (8-17): Regular orders only
   - Test outside hours (18-7): Fakeout logic active
   - Verify fakeout orders placed on range breaks

2. **Multi-Trade Test**
   ```
   NumBreakoutTrades = 2
   LotSize1 = 0.01
   LotSize2 = 0.02
   TP1 = 500
   TP2 = 1000
   ```
   - Verify multiple orders per direction
   - Check individual lot sizes and TPs

3. **Breakeven and Trailing Test**
   - Place manual trades or wait for EA trades
   - Monitor breakeven activation
   - Monitor trailing stop behavior
   - Verify in info panel and trade history

### Phase 3: Stress Testing (Demo Account)
1. **High Volatility Test**
   - Test during news events
   - Test with volatile pairs (GBP/JPY, etc.)
   - Monitor spread filtering
   - Check ADR filtering if enabled

2. **Session Transition Test**
   - Test over weekend
   - Test during daylight saving changes
   - Verify daily resets work correctly
   - Check range time calculations

3. **Error Handling Test**
   - Test with invalid inputs
   - Test with no internet connection
   - Test broker rejection scenarios
   - Verify error messages and recovery

## Monitoring Checklist

### Real-time Monitoring
- [ ] Info panel updates correctly
- [ ] Range box displays properly
- [ ] Pending orders placed at correct levels
- [ ] Risk calculations accurate
- [ ] Daily/weekly loss tracking works
- [ ] Breakeven/trailing stops function
- [ ] Enhanced fakeout logic activates correctly

### Log File Monitoring
Check MT4 Experts tab for:
- [ ] Range identification messages
- [ ] Order placement confirmations
- [ ] Risk calculation debug info
- [ ] Error messages and warnings
- [ ] Daily reset confirmations

### Performance Metrics
Track over 1-2 weeks:
- [ ] Win rate vs expected
- [ ] Average R-multiple achieved
- [ ] Maximum drawdown
- [ ] Daily/weekly P&L consistency
- [ ] Order execution quality

## Common Issues and Solutions

### Issue: Range not identified
**Solution:** Check range times, ensure sufficient historical data

### Issue: No pending orders placed
**Solution:** Check spread limits, ADR filters, custom trading hours

### Issue: Orders not triggering
**Solution:** Verify broker allows pending orders, check minimum distance

### Issue: Fakeout logic not working
**Solution:** Verify custom hours settings, check range identification

### Issue: High slippage
**Solution:** Adjust slippage parameter, test during different sessions

## Production Deployment

### Before Live Trading
1. Complete all demo testing phases
2. Verify consistent performance over 2+ weeks
3. Test with small live account first
4. Monitor for 1 week before increasing size
5. Set conservative risk limits initially

### Live Trading Checklist
- [ ] VPS setup with stable connection
- [ ] Backup EA file and settings
- [ ] Monitor first few trades closely
- [ ] Keep trading journal
- [ ] Regular performance review

## Support and Maintenance

### Regular Checks
- Weekly performance review
- Monthly settings optimization
- Quarterly strategy assessment
- Update broker lot info if changed

### Contact Information
- Developer: Bryan Alvin Bagorogoza
- Email: <EMAIL>
- Website: https://innovationxinternational.com

## Version History
- v2.0: Enhanced fakeout logic, custom trading hours
- Current: Debugging fixes and improvements