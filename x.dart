```mql4
// Innovationx International Range Breakout X.mq4

// ...existing code...

datetime lastTradeDayBuy = 0;
datetime lastTradeDaySell = 0;

// ...existing code...

void PlaceBreakoutTrades(double stopLossPoints) {
    // ...existing code...
    datetime today = DateOfDay(TimeCurrent());

    // For Buy
    if (/* condition to place buy stop */) {
        if (lastTradeDayBuy != today) {
            // ...place buy stop order...
            lastTradeDayBuy = today;
        } else {
            Print("Buy trade already placed today, skipping.");
        }
    }

    // For Sell
    if (/* condition to place sell stop */) {
        if (lastTradeDaySell != today) {
            // ...place sell stop order...
            lastTradeDaySell = today;
        } else {
            Print("Sell trade already placed today, skipping.");
        }
    }
    // ...existing code...
}

// Utility: Get date at midnight (00:00) for comparison
datetime DateOfDay(datetime t) {
    return StringToTime(TimeToString(t, TIME_DATE));
}

// ...existing code...
```